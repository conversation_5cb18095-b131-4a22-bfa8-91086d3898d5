{"name": "hrms-backend", "version": "1.0.0", "description": "HRMS Backend API built with Express.js and MongoDB", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["hrms", "express", "mongodb", "api", "backend"], "author": "", "license": "ISC", "dependencies": {"bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^17.0.1", "express": "^5.1.0", "express-rate-limit": "^7.5.1", "express-validator": "^7.2.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.16.1", "morgan": "^1.10.0"}, "devDependencies": {"concurrently": "^9.2.0", "nodemon": "^3.1.10"}}